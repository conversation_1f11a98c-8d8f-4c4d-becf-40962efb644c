# 🚀 Smart Lamp Laravel Dashboard - Setup Instructions

## 📋 **Requirements**

### **Server Requirements:**
- **PHP 8.0+** (Recommended: PHP 8.1+)
- **Composer** (Latest version)
- **MySQL 5.7+** atau **MariaDB 10.3+**
- **Node.js 16+** dan **NPM** (untuk asset compilation)
- **Web Server** (Apache/Nginx)

### **Hostinger Specific:**
- **Business Plan** atau lebih tinggi
- **SSH Access** (untuk Composer dan Artisan commands)
- **Database Access**
- **SSL Certificate**

---

## 🛠️ **Installation Steps**

### **Step 1: Upload dan Extract**

1. **Upload** semua file Laravel ke hosting
2. **Extract** di folder yang tepat:
   ```
   /public_html/smartlamp/  (untuk subdomain)
   atau
   /public_html/           (untuk domain utama)
   ```

### **Step 2: Install Dependencies**

**Via SSH (Recommended):**
```bash
# Masuk ke folder project
cd /path/to/your/project

# Install PHP dependencies
composer install --optimize-autoloader --no-dev

# Install Node dependencies (jika ada)
npm install
npm run production
```

**Jika tidak ada SSH access:**
- Upload folder `vendor` yang sudah di-generate di local
- Upload folder `node_modules` dan compiled assets

### **Step 3: Environment Configuration**

1. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` file:**
   ```env
   APP_NAME="Smart Lamp Dashboard"
   APP_ENV=production
   APP_KEY=base64:YOUR_APP_KEY_HERE
   APP_DEBUG=false
   APP_URL=https://yourdomain.com

   DB_CONNECTION=mysql
   DB_HOST=localhost
   DB_PORT=3306
   DB_DATABASE=your_database_name
   DB_USERNAME=your_username
   DB_PASSWORD=your_password

   # MQTT Configuration
   MQTT_HOST=broker.hivemq.com
   MQTT_PORT=1883
   MQTT_TOPIC_DATA=smartlamp/data
   MQTT_TOPIC_COMMAND=smartlamp/cmd

   # Dashboard Settings
   DASHBOARD_DATA_RETENTION_DAYS=365
   DASHBOARD_TIMEZONE=Asia/Jakarta
   ```

3. **Generate Application Key:**
   ```bash
   php artisan key:generate
   ```

### **Step 4: Database Setup**

1. **Create Database** di Hostinger cPanel
2. **Run Migrations:**
   ```bash
   php artisan migrate
   ```

3. **Seed Initial Data (Optional):**
   ```bash
   php artisan db:seed
   ```

### **Step 5: File Permissions**

Set proper permissions:
```bash
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
chmod -R 755 public/exports/
```

### **Step 6: Web Server Configuration**

**Apache (.htaccess):**
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>
```

**Nginx:**
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
    include fastcgi_params;
}
```

---

## 🔧 **Configuration**

### **MQTT Integration**

Edit ESP32 code untuk mengirim data ke Laravel:
```cpp
// Update ESP32 main.cpp
const char* api_endpoint = "https://yourdomain.com/api/sensor-data";

// Dalam fungsi sendToMQTT, tambahkan HTTP POST:
void sendToHTTP(String jsonData) {
    HTTPClient http;
    http.begin(api_endpoint);
    http.addHeader("Content-Type", "application/json");
    
    int httpResponseCode = http.POST(jsonData);
    
    if (httpResponseCode > 0) {
        Serial.println("✓ Data sent to Laravel API");
    } else {
        Serial.println("✗ Failed to send to Laravel API");
    }
    
    http.end();
}
```

### **Scheduled Tasks (Cron Jobs)**

Add to crontab:
```bash
# Laravel Scheduler
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1

# Data cleanup (daily)
0 2 * * * cd /path/to/your/project && php artisan app:cleanup-old-data

# Export cleanup (weekly)
0 3 * * 0 cd /path/to/your/project && php artisan app:cleanup-exports
```

### **Queue Workers (Optional)**

For background processing:
```bash
# Install Supervisor
sudo apt install supervisor

# Create worker config
sudo nano /etc/supervisor/conf.d/laravel-worker.conf
```

---

## 📊 **Features Overview**

### **Dashboard Features:**
- ✅ **Real-time monitoring** dengan auto-refresh
- ✅ **Interactive charts** (Chart.js)
- ✅ **Device management** dan status monitoring
- ✅ **Data filtering** berdasarkan device dan tanggal
- ✅ **Responsive design** untuk mobile

### **API Endpoints:**
- ✅ **RESTful API** untuk semua operasi
- ✅ **Rate limiting** dan security
- ✅ **Bulk operations** untuk data besar
- ✅ **Health check** endpoint
- ✅ **Comprehensive error handling**

### **Export System:**
- ✅ **Multiple formats** (CSV, JSON, Excel)
- ✅ **Date range selection**
- ✅ **Background processing** untuk file besar
- ✅ **Download history** tracking
- ✅ **Automatic cleanup** expired files

### **Data Management:**
- ✅ **Automatic data retention**
- ✅ **Database optimization** dengan indexing
- ✅ **Bulk insert** untuk performance
- ✅ **Data validation** dan sanitization

---

## 🔒 **Security**

### **Production Security:**
```env
APP_DEBUG=false
APP_ENV=production
```

### **Database Security:**
- Use strong passwords
- Limit database user privileges
- Enable SSL connections

### **File Security:**
```bash
# Secure sensitive files
chmod 600 .env
chmod -R 755 storage/
```

### **API Security:**
- Rate limiting enabled
- CSRF protection
- Input validation
- SQL injection protection

---

## 🚀 **Performance Optimization**

### **Laravel Optimization:**
```bash
# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Optimize autoloader
composer dump-autoload --optimize
```

### **Database Optimization:**
- Proper indexing (already included)
- Query optimization
- Connection pooling
- Regular maintenance

### **Frontend Optimization:**
- CDN for external libraries
- Asset minification
- Browser caching
- Lazy loading

---

## 📱 **Mobile Support**

Dashboard fully responsive:
- ✅ **Mobile-first design**
- ✅ **Touch-friendly controls**
- ✅ **Optimized charts** untuk mobile
- ✅ **Progressive Web App** ready

---

## 🔧 **Troubleshooting**

### **Common Issues:**

**1. 500 Internal Server Error:**
```bash
# Check logs
tail -f storage/logs/laravel.log

# Fix permissions
chmod -R 755 storage/ bootstrap/cache/
```

**2. Database Connection Error:**
```bash
# Test connection
php artisan tinker
>>> DB::connection()->getPdo();
```

**3. Composer Issues:**
```bash
# Clear cache
composer clear-cache
composer install --no-cache
```

**4. Migration Errors:**
```bash
# Reset migrations (CAUTION: Data loss)
php artisan migrate:fresh

# Or rollback and re-run
php artisan migrate:rollback
php artisan migrate
```

### **Debug Mode:**
```env
# Enable for debugging (disable in production)
APP_DEBUG=true
LOG_LEVEL=debug
```

---

## 📞 **Support**

### **Log Files:**
- **Laravel Logs**: `storage/logs/laravel.log`
- **Web Server Logs**: Check cPanel error logs
- **Database Logs**: MySQL error logs

### **Health Check:**
Visit: `https://yourdomain.com/api/health`

### **API Documentation:**
Available endpoints:
- `GET /api/devices` - List all devices
- `POST /api/sensor-data` - Store sensor data
- `GET /api/sensor-data` - Retrieve data with filters
- `POST /api/devices/{id}/command` - Send commands

Laravel dashboard siap untuk production dengan fitur lengkap dan performa optimal!

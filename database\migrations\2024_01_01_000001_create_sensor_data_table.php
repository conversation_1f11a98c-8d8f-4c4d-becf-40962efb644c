<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sensor_data', function (Blueprint $table) {
            $table->id();
            $table->timestamp('sensor_timestamp');
            $table->string('device_id', 50)->index();
            $table->unsignedInteger('counter')->default(0);
            
            // Sensor readings
            $table->decimal('temperature', 5, 2)->nullable();
            $table->decimal('humidity', 5, 2)->nullable();
            $table->decimal('light', 8, 2)->nullable();
            $table->decimal('voltage', 6, 3)->nullable();
            $table->decimal('current', 8, 2)->nullable();
            $table->decimal('power', 10, 3)->nullable();
            
            // Sensor status
            $table->boolean('dht11_status')->default(false);
            $table->boolean('bh1750_status')->default(false);
            $table->boolean('ina219_status')->default(false);
            
            // System info
            $table->unsignedInteger('interval_ms')->default(30000);
            $table->json('raw_data')->nullable(); // Store original JSON
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index('sensor_timestamp');
            $table->index(['device_id', 'sensor_timestamp']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sensor_data');
    }
};

<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ExportController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Redirect root to dashboard
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Dashboard routes
Route::prefix('dashboard')->name('dashboard.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('index');
    Route::get('/logs', [DashboardController::class, 'logs'])->name('logs');
    
    // AJAX endpoints for dashboard
    Route::get('/realtime-data', [DashboardController::class, 'getRealtimeData'])->name('realtime-data');
    Route::get('/statistics', [DashboardController::class, 'getStatistics'])->name('statistics');
    Route::get('/chart-data', [DashboardController::class, 'getChartData'])->name('chart-data');
    Route::get('/device-status', [DashboardController::class, 'getDeviceStatus'])->name('device-status');
});

// Export routes
Route::prefix('export')->name('export.')->group(function () {
    Route::post('/data', [ExportController::class, 'exportData'])->name('data');
    Route::get('/download/{filename}', [ExportController::class, 'downloadFile'])->name('download');
    Route::get('/history', [ExportController::class, 'exportHistory'])->name('history');
    Route::delete('/cleanup', [ExportController::class, 'cleanupExpiredFiles'])->name('cleanup');
});

// Fallback route for dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

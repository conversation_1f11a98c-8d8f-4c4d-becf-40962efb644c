<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('system_logs', function (Blueprint $table) {
            $table->id();
            $table->timestamp('log_timestamp');
            $table->string('device_id', 50)->index();
            $table->enum('level', ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'])->default('INFO');
            $table->string('event_type', 100)->index(); // e.g., 'data_received', 'sensor_failure', 'export_completed'
            $table->text('message');
            $table->json('context')->nullable(); // Additional data
            $table->string('ip_address', 45)->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index('log_timestamp');
            $table->index(['device_id', 'log_timestamp']);
            $table->index(['level', 'log_timestamp']);
            $table->index('event_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('system_logs');
    }
};

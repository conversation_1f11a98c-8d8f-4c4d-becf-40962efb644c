<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class SensorData extends Model
{
    use HasFactory;

    protected $table = 'sensor_data';

    protected $fillable = [
        'sensor_timestamp',
        'device_id',
        'counter',
        'temperature',
        'humidity',
        'light',
        'voltage',
        'current',
        'power',
        'dht11_status',
        'bh1750_status',
        'ina219_status',
        'interval_ms',
        'raw_data'
    ];

    protected $casts = [
        'sensor_timestamp' => 'datetime',
        'temperature' => 'decimal:2',
        'humidity' => 'decimal:2',
        'light' => 'decimal:2',
        'voltage' => 'decimal:3',
        'current' => 'decimal:2',
        'power' => 'decimal:3',
        'dht11_status' => 'boolean',
        'bh1750_status' => 'boolean',
        'ina219_status' => 'boolean',
        'raw_data' => 'array'
    ];

    protected $dates = [
        'sensor_timestamp',
        'created_at',
        'updated_at'
    ];

    // Relationships
    public function deviceSettings()
    {
        return $this->belongsTo(DeviceSettings::class, 'device_id', 'device_id');
    }

    // Scopes
    public function scopeForDevice(Builder $query, string $deviceId): Builder
    {
        return $query->where('device_id', $deviceId);
    }

    public function scopeInDateRange(Builder $query, Carbon $startDate, Carbon $endDate): Builder
    {
        return $query->whereBetween('sensor_timestamp', [$startDate, $endDate]);
    }

    public function scopeRecent(Builder $query, int $hours = 24): Builder
    {
        return $query->where('sensor_timestamp', '>=', now()->subHours($hours));
    }

    public function scopeWithValidTemperature(Builder $query): Builder
    {
        return $query->whereNotNull('temperature')
                    ->where('dht11_status', true);
    }

    public function scopeWithValidHumidity(Builder $query): Builder
    {
        return $query->whereNotNull('humidity')
                    ->where('dht11_status', true);
    }

    public function scopeWithValidLight(Builder $query): Builder
    {
        return $query->whereNotNull('light')
                    ->where('light', '>=', 0)
                    ->where('bh1750_status', true);
    }

    public function scopeWithValidPower(Builder $query): Builder
    {
        return $query->whereNotNull('voltage')
                    ->whereNotNull('current')
                    ->where('ina219_status', true);
    }

    // Accessors
    public function getPowerCalculatedAttribute(): ?float
    {
        if ($this->voltage !== null && $this->current !== null) {
            return round($this->voltage * $this->current, 3);
        }
        return $this->power;
    }

    public function getSensorStatusAttribute(): array
    {
        return [
            'dht11' => $this->dht11_status,
            'bh1750' => $this->bh1750_status,
            'ina219' => $this->ina219_status
        ];
    }

    public function getWorkingSensorsCountAttribute(): int
    {
        return collect($this->sensor_status)->filter()->count();
    }

    public function getFormattedTimestampAttribute(): string
    {
        return $this->sensor_timestamp->format('Y-m-d H:i:s');
    }

    // Static methods for statistics
    public static function getStatistics(string $deviceId = null, Carbon $startDate = null, Carbon $endDate = null): array
    {
        $query = static::query();

        if ($deviceId) {
            $query->forDevice($deviceId);
        }

        if ($startDate && $endDate) {
            $query->inDateRange($startDate, $endDate);
        }

        $data = $query->get();

        return [
            'total_records' => $data->count(),
            'temperature' => [
                'min' => $data->whereNotNull('temperature')->min('temperature'),
                'max' => $data->whereNotNull('temperature')->max('temperature'),
                'avg' => $data->whereNotNull('temperature')->avg('temperature'),
                'count' => $data->whereNotNull('temperature')->count()
            ],
            'humidity' => [
                'min' => $data->whereNotNull('humidity')->min('humidity'),
                'max' => $data->whereNotNull('humidity')->max('humidity'),
                'avg' => $data->whereNotNull('humidity')->avg('humidity'),
                'count' => $data->whereNotNull('humidity')->count()
            ],
            'light' => [
                'min' => $data->where('light', '>=', 0)->min('light'),
                'max' => $data->where('light', '>=', 0)->max('light'),
                'avg' => $data->where('light', '>=', 0)->avg('light'),
                'count' => $data->where('light', '>=', 0)->count()
            ],
            'power' => [
                'min' => $data->whereNotNull('power')->min('power'),
                'max' => $data->whereNotNull('power')->max('power'),
                'avg' => $data->whereNotNull('power')->avg('power'),
                'total' => $data->whereNotNull('power')->sum('power'),
                'count' => $data->whereNotNull('power')->count()
            ],
            'sensor_reliability' => [
                'dht11' => $data->count() > 0 ? ($data->where('dht11_status', true)->count() / $data->count()) * 100 : 0,
                'bh1750' => $data->count() > 0 ? ($data->where('bh1750_status', true)->count() / $data->count()) * 100 : 0,
                'ina219' => $data->count() > 0 ? ($data->where('ina219_status', true)->count() / $data->count()) * 100 : 0,
            ]
        ];
    }

    public static function getHourlyData(string $deviceId = null, int $hours = 24): array
    {
        $query = static::query()
            ->selectRaw('
                DATE_FORMAT(sensor_timestamp, "%Y-%m-%d %H:00:00") as hour,
                COUNT(*) as record_count,
                AVG(CASE WHEN temperature IS NOT NULL THEN temperature END) as avg_temperature,
                AVG(CASE WHEN humidity IS NOT NULL THEN humidity END) as avg_humidity,
                AVG(CASE WHEN light IS NOT NULL AND light >= 0 THEN light END) as avg_light,
                AVG(CASE WHEN power IS NOT NULL THEN power END) as avg_power
            ')
            ->where('sensor_timestamp', '>=', now()->subHours($hours))
            ->groupByRaw('DATE_FORMAT(sensor_timestamp, "%Y-%m-%d %H:00:00")')
            ->orderBy('hour');

        if ($deviceId) {
            $query->forDevice($deviceId);
        }

        return $query->get()->toArray();
    }

    // Cleanup old data
    public static function cleanupOldData(int $daysToKeep = 365): int
    {
        return static::where('sensor_timestamp', '<', now()->subDays($daysToKeep))->delete();
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('device_settings', function (Blueprint $table) {
            $table->id();
            $table->string('device_id', 50)->unique();
            $table->string('device_name')->nullable();
            $table->string('location')->nullable();
            $table->text('description')->nullable();
            
            // Device configuration
            $table->unsignedInteger('data_interval')->default(30000); // milliseconds
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_seen')->nullable();
            $table->timestamp('first_seen')->nullable();
            
            // Sensor configuration
            $table->json('sensor_config')->nullable(); // Sensor-specific settings
            $table->json('alert_thresholds')->nullable(); // Alert thresholds for each sensor
            
            // Statistics
            $table->unsignedBigInteger('total_data_points')->default(0);
            $table->timestamp('last_data_received')->nullable();
            $table->decimal('uptime_percentage', 5, 2)->default(0);
            
            $table->timestamps();
            
            // Indexes
            $table->index('device_id');
            $table->index('is_active');
            $table->index('last_seen');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('device_settings');
    }
};

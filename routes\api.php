<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApiController;
use App\Http\Controllers\ExportController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Health check endpoint
Route::get('/health', [ApiController::class, 'health'])->name('api.health');

// Device management
Route::prefix('devices')->name('api.devices.')->group(function () {
    Route::get('/', [ApiController::class, 'getDevices'])->name('index');
    Route::get('/{deviceId}/status', [ApiController::class, 'getDeviceStatus'])->name('status');
    Route::post('/{deviceId}/command', [ApiController::class, 'sendCommand'])->name('command');
});

// Sensor data endpoints
Route::prefix('sensor-data')->name('api.sensor-data.')->group(function () {
    Route::post('/', [ApiController::class, 'storeSensorData'])->name('store');
    Route::get('/', [ApiController::class, 'getSensorData'])->name('index');
    Route::get('/statistics', [ApiController::class, 'getStatistics'])->name('statistics');
    Route::get('/export', [ExportController::class, 'exportData'])->name('export');
});

// MQTT webhook endpoint (for external MQTT services)
Route::post('/mqtt/webhook', [ApiController::class, 'mqttWebhook'])->name('api.mqtt.webhook');

// Bulk operations
Route::prefix('bulk')->name('api.bulk.')->group(function () {
    Route::post('/sensor-data', [ApiController::class, 'storeBulkSensorData'])->name('sensor-data');
    Route::delete('/cleanup', [ApiController::class, 'cleanupOldData'])->name('cleanup');
});

// Export endpoints
Route::prefix('export')->name('api.export.')->group(function () {
    Route::post('/request', [ExportController::class, 'requestExport'])->name('request');
    Route::get('/status/{exportId}', [ExportController::class, 'getExportStatus'])->name('status');
    Route::get('/download/{exportId}', [ExportController::class, 'downloadExport'])->name('download');
    Route::get('/history', [ExportController::class, 'getExportHistory'])->name('history');
});

// Legacy endpoints for backward compatibility
Route::post('/data', [ApiController::class, 'storeSensorData'])->name('api.data.store');
Route::get('/data', [ApiController::class, 'getSensorData'])->name('api.data.index');
Route::get('/devices', [ApiController::class, 'getDevices'])->name('api.devices');
Route::post('/command', [ApiController::class, 'sendCommand'])->name('api.command');

// Rate limited endpoints
Route::middleware(['throttle:60,1'])->group(function () {
    Route::post('/export/bulk', [ExportController::class, 'bulkExport'])->name('api.export.bulk');
    Route::get('/analytics/report', [ApiController::class, 'generateAnalyticsReport'])->name('api.analytics.report');
});

// Admin endpoints (you can add authentication middleware here)
Route::prefix('admin')->name('api.admin.')->group(function () {
    Route::get('/system-info', [ApiController::class, 'getSystemInfo'])->name('system-info');
    Route::post('/maintenance/cleanup', [ApiController::class, 'performMaintenance'])->name('maintenance.cleanup');
    Route::get('/logs', [ApiController::class, 'getSystemLogs'])->name('logs');
});

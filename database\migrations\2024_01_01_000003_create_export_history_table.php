<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('export_history', function (Blueprint $table) {
            $table->id();
            $table->string('filename');
            $table->enum('format', ['CSV', 'JSON', 'EXCEL', 'PDF'])->default('CSV');
            $table->timestamp('start_date');
            $table->timestamp('end_date');
            $table->unsignedInteger('record_count')->default(0);
            $table->unsignedBigInteger('file_size')->default(0); // in bytes
            $table->string('file_path')->nullable();
            $table->unsignedInteger('download_count')->default(0);
            $table->timestamp('expires_at')->nullable();
            $table->string('exported_by')->nullable(); // IP or user identifier
            $table->json('filters')->nullable(); // Store export filters
            $table->enum('status', ['PENDING', 'COMPLETED', 'FAILED', 'EXPIRED'])->default('PENDING');
            $table->text('error_message')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index('created_at');
            $table->index('status');
            $table->index('expires_at');
            $table->index(['start_date', 'end_date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('export_history');
    }
};

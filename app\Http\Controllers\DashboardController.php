<?php

namespace App\Http\Controllers;

use App\Models\SensorData;
use App\Models\DeviceSettings;
use App\Models\SystemLogs;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        $devices = DeviceSettings::getDeviceList();
        $onlineDevices = DeviceSettings::getOnlineDevicesCount();
        $totalDevices = DeviceSettings::getTotalDevicesCount();
        
        // Get latest data for each device
        $latestData = [];
        foreach ($devices as $device) {
            $latest = SensorData::forDevice($device['device_id'])
                              ->latest('sensor_timestamp')
                              ->first();
            if ($latest) {
                $latestData[$device['device_id']] = $latest;
            }
        }

        return view('dashboard.index', compact('devices', 'onlineDevices', 'totalDevices', 'latestData'));
    }

    public function getRealtimeData(Request $request): JsonResponse
    {
        $deviceId = $request->get('device_id');
        $limit = $request->get('limit', 100);

        $query = SensorData::query()
                          ->orderBy('sensor_timestamp', 'desc')
                          ->limit($limit);

        if ($deviceId) {
            $query->forDevice($deviceId);
        }

        $data = $query->get()->map(function ($item) {
            return [
                'id' => $item->id,
                'timestamp' => $item->sensor_timestamp->toISOString(),
                'device_id' => $item->device_id,
                'counter' => $item->counter,
                'temperature' => $item->temperature,
                'humidity' => $item->humidity,
                'light' => $item->light,
                'voltage' => $item->voltage,
                'current' => $item->current,
                'power' => $item->power_calculated,
                'sensor_status' => $item->sensor_status,
                'interval_ms' => $item->interval_ms,
                'working_sensors' => $item->working_sensors_count
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data,
            'count' => $data->count(),
            'timestamp' => now()->toISOString()
        ]);
    }

    public function getStatistics(Request $request): JsonResponse
    {
        $deviceId = $request->get('device_id');
        $period = $request->get('period', 'day'); // hour, day, week, month, year
        
        // Calculate date range
        $endDate = now();
        $startDate = match($period) {
            'hour' => $endDate->copy()->subHour(),
            'day' => $endDate->copy()->subDay(),
            'week' => $endDate->copy()->subWeek(),
            'month' => $endDate->copy()->subMonth(),
            'year' => $endDate->copy()->subYear(),
            default => $endDate->copy()->subDay()
        };

        $statistics = SensorData::getStatistics($deviceId, $startDate, $endDate);
        $hourlyData = SensorData::getHourlyData($deviceId, 24);
        $devices = DeviceSettings::getDeviceList();

        return response()->json([
            'success' => true,
            'period' => $period,
            'date_range' => [
                'start' => $startDate->toISOString(),
                'end' => $endDate->toISOString()
            ],
            'statistics' => $statistics,
            'hourly_data' => $hourlyData,
            'devices' => $devices,
            'summary' => [
                'total_devices' => count($devices),
                'online_devices' => collect($devices)->where('is_online', true)->count(),
                'total_records' => $statistics['total_records'],
                'data_coverage' => [
                    'temperature' => $statistics['temperature']['count'],
                    'humidity' => $statistics['humidity']['count'],
                    'light' => $statistics['light']['count'],
                    'power' => $statistics['power']['count']
                ]
            ]
        ]);
    }

    public function getChartData(Request $request): JsonResponse
    {
        $deviceId = $request->get('device_id');
        $hours = $request->get('hours', 24);
        $limit = $request->get('limit', 100);

        $query = SensorData::query()
                          ->where('sensor_timestamp', '>=', now()->subHours($hours))
                          ->orderBy('sensor_timestamp', 'asc');

        if ($deviceId) {
            $query->forDevice($deviceId);
        }

        if ($limit > 0) {
            // Get evenly distributed data points
            $total = $query->count();
            if ($total > $limit) {
                $step = intval($total / $limit);
                $query->whereRaw('MOD(id, ?) = 0', [$step]);
            }
        }

        $data = $query->get();

        $chartData = [
            'labels' => $data->pluck('sensor_timestamp')->map(function ($timestamp) {
                return $timestamp->format('H:i:s');
            }),
            'datasets' => [
                'temperature' => $data->pluck('temperature'),
                'humidity' => $data->pluck('humidity'),
                'light' => $data->pluck('light'),
                'voltage' => $data->pluck('voltage'),
                'current' => $data->pluck('current'),
                'power' => $data->map(function ($item) {
                    return $item->power_calculated;
                })
            ]
        ];

        return response()->json([
            'success' => true,
            'chart_data' => $chartData,
            'data_points' => $data->count(),
            'time_range' => [
                'start' => $data->first()?->sensor_timestamp?->toISOString(),
                'end' => $data->last()?->sensor_timestamp?->toISOString()
            ]
        ]);
    }

    public function getDeviceStatus(Request $request): JsonResponse
    {
        $deviceId = $request->get('device_id');
        
        if ($deviceId) {
            $device = DeviceSettings::where('device_id', $deviceId)->first();
            if (!$device) {
                return response()->json(['error' => 'Device not found'], 404);
            }
            
            $latestData = $device->latestSensorData;
            $statistics = $device->getStatistics(now()->subDay(), now());
            
            return response()->json([
                'success' => true,
                'device' => [
                    'device_id' => $device->device_id,
                    'device_name' => $device->device_name,
                    'location' => $device->location,
                    'status' => $device->status,
                    'is_online' => $device->is_online,
                    'last_seen' => $device->last_seen?->toISOString(),
                    'uptime_percentage' => $device->uptime_percentage,
                    'data_interval' => $device->data_interval,
                    'total_data_points' => $device->total_data_points
                ],
                'latest_data' => $latestData ? [
                    'timestamp' => $latestData->sensor_timestamp->toISOString(),
                    'temperature' => $latestData->temperature,
                    'humidity' => $latestData->humidity,
                    'light' => $latestData->light,
                    'voltage' => $latestData->voltage,
                    'current' => $latestData->current,
                    'power' => $latestData->power_calculated,
                    'sensor_status' => $latestData->sensor_status
                ] : null,
                'statistics' => $statistics
            ]);
        }

        // Return all devices status
        $devices = DeviceSettings::with('latestSensorData')->get();
        
        $devicesStatus = $devices->map(function ($device) {
            return [
                'device_id' => $device->device_id,
                'device_name' => $device->device_name,
                'status' => $device->status,
                'is_online' => $device->is_online,
                'last_seen' => $device->last_seen?->toISOString(),
                'latest_data' => $device->latestSensorData ? [
                    'timestamp' => $device->latestSensorData->sensor_timestamp->toISOString(),
                    'sensor_status' => $device->latestSensorData->sensor_status,
                    'working_sensors' => $device->latestSensorData->working_sensors_count
                ] : null
            ];
        });

        return response()->json([
            'success' => true,
            'devices' => $devicesStatus,
            'summary' => [
                'total' => $devices->count(),
                'online' => $devices->where('is_online', true)->count(),
                'offline' => $devices->where('is_online', false)->count(),
                'active' => $devices->where('is_active', true)->count()
            ]
        ]);
    }

    public function logs(Request $request)
    {
        $deviceId = $request->get('device_id');
        $period = $request->get('period', 'day');
        $limit = $request->get('limit', 100);
        $page = $request->get('page', 1);

        // Calculate date range
        $endDate = now();
        $startDate = match($period) {
            'hour' => $endDate->copy()->subHour(),
            'day' => $endDate->copy()->subDay(),
            'week' => $endDate->copy()->subWeek(),
            'month' => $endDate->copy()->subMonth(),
            'year' => $endDate->copy()->subYear(),
            default => $endDate->copy()->subDay()
        };

        $query = SensorData::query()
                          ->inDateRange($startDate, $endDate)
                          ->orderBy('sensor_timestamp', 'desc');

        if ($deviceId) {
            $query->forDevice($deviceId);
        }

        $data = $query->paginate($limit, ['*'], 'page', $page);
        $statistics = SensorData::getStatistics($deviceId, $startDate, $endDate);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => $data->items(),
                'pagination' => [
                    'current_page' => $data->currentPage(),
                    'last_page' => $data->lastPage(),
                    'per_page' => $data->perPage(),
                    'total' => $data->total(),
                    'has_more' => $data->hasMorePages()
                ],
                'statistics' => $statistics,
                'period' => $period
            ]);
        }

        $devices = DeviceSettings::getDeviceList();
        
        return view('dashboard.logs', compact('data', 'devices', 'statistics', 'period', 'deviceId'));
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class DeviceSettings extends Model
{
    use HasFactory;

    protected $fillable = [
        'device_id',
        'device_name',
        'location',
        'description',
        'data_interval',
        'is_active',
        'last_seen',
        'first_seen',
        'sensor_config',
        'alert_thresholds',
        'total_data_points',
        'last_data_received',
        'uptime_percentage'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'last_seen' => 'datetime',
        'first_seen' => 'datetime',
        'last_data_received' => 'datetime',
        'sensor_config' => 'array',
        'alert_thresholds' => 'array',
        'uptime_percentage' => 'decimal:2'
    ];

    // Relationships
    public function sensorData()
    {
        return $this->hasMany(SensorData::class, 'device_id', 'device_id');
    }

    public function recentSensorData()
    {
        return $this->hasMany(SensorData::class, 'device_id', 'device_id')
                   ->orderBy('sensor_timestamp', 'desc');
    }

    public function latestSensorData()
    {
        return $this->hasOne(SensorData::class, 'device_id', 'device_id')
                   ->latest('sensor_timestamp');
    }

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeOnline(Builder $query, int $minutesThreshold = 5): Builder
    {
        return $query->where('last_seen', '>=', now()->subMinutes($minutesThreshold));
    }

    public function scopeOffline(Builder $query, int $minutesThreshold = 5): Builder
    {
        return $query->where('last_seen', '<', now()->subMinutes($minutesThreshold))
                    ->orWhereNull('last_seen');
    }

    // Accessors
    public function getIsOnlineAttribute(): bool
    {
        if (!$this->last_seen) {
            return false;
        }
        
        return $this->last_seen->diffInMinutes(now()) <= 5;
    }

    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }
        
        return $this->is_online ? 'online' : 'offline';
    }

    public function getDataIntervalSecondsAttribute(): float
    {
        return $this->data_interval / 1000;
    }

    public function getDataIntervalMinutesAttribute(): float
    {
        return $this->data_interval / 60000;
    }

    public function getUptimeHoursAttribute(): ?float
    {
        if (!$this->first_seen || !$this->last_seen) {
            return null;
        }
        
        return $this->first_seen->diffInHours($this->last_seen);
    }

    public function getDefaultAlertThresholdsAttribute(): array
    {
        return [
            'temperature' => [
                'min' => 0,
                'max' => 50,
                'enabled' => true
            ],
            'humidity' => [
                'min' => 0,
                'max' => 100,
                'enabled' => true
            ],
            'light' => [
                'min' => 0,
                'max' => 10000,
                'enabled' => true
            ],
            'voltage' => [
                'min' => 0,
                'max' => 5,
                'enabled' => true
            ],
            'current' => [
                'min' => -1000,
                'max' => 1000,
                'enabled' => true
            ]
        ];
    }

    // Methods
    public function updateLastSeen(): void
    {
        $this->update([
            'last_seen' => now(),
            'last_data_received' => now()
        ]);
    }

    public function incrementDataPoints(): void
    {
        $this->increment('total_data_points');
    }

    public function calculateUptime(): float
    {
        if (!$this->first_seen) {
            return 0;
        }

        $totalHours = $this->first_seen->diffInHours(now());
        if ($totalHours === 0) {
            return 100;
        }

        // Calculate based on data frequency
        $expectedDataPoints = $totalHours * 3600 / ($this->data_interval / 1000);
        $actualDataPoints = $this->total_data_points;

        $uptime = min(100, ($actualDataPoints / $expectedDataPoints) * 100);
        
        $this->update(['uptime_percentage' => $uptime]);
        
        return $uptime;
    }

    public function getStatistics(Carbon $startDate = null, Carbon $endDate = null): array
    {
        return SensorData::getStatistics($this->device_id, $startDate, $endDate);
    }

    public function getHourlyData(int $hours = 24): array
    {
        return SensorData::getHourlyData($this->device_id, $hours);
    }

    // Static methods
    public static function findOrCreateDevice(string $deviceId): self
    {
        return static::firstOrCreate(
            ['device_id' => $deviceId],
            [
                'device_name' => "Smart Lamp {$deviceId}",
                'is_active' => true,
                'first_seen' => now(),
                'data_interval' => 30000,
                'sensor_config' => [],
                'alert_thresholds' => []
            ]
        );
    }

    public static function getDeviceList(): array
    {
        return static::select('device_id', 'device_name', 'location', 'is_active', 'last_seen')
                    ->orderBy('last_seen', 'desc')
                    ->get()
                    ->map(function ($device) {
                        return [
                            'device_id' => $device->device_id,
                            'device_name' => $device->device_name,
                            'location' => $device->location,
                            'status' => $device->status,
                            'is_online' => $device->is_online,
                            'last_seen' => $device->last_seen?->format('Y-m-d H:i:s'),
                            'data_interval' => $device->data_interval_seconds
                        ];
                    })
                    ->toArray();
    }

    public static function getOnlineDevicesCount(): int
    {
        return static::online()->count();
    }

    public static function getTotalDevicesCount(): int
    {
        return static::count();
    }
}

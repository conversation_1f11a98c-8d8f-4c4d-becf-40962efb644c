// Smart Lamp Dashboard JavaScript
class SmartLampDashboard {
    constructor() {
        this.sensorData = [];
        this.maxDataPoints = 100;
        this.currentDevice = null;
        this.updateInterval = null;
        this.charts = {};
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeCharts();
        this.startDataUpdates();
    }

    setupEventListeners() {
        // Device selector
        const deviceSelect = document.getElementById('deviceSelect');
        if (deviceSelect) {
            deviceSelect.addEventListener('change', (e) => {
                this.currentDevice = e.target.value || null;
                this.loadData();
            });
        }

        // Data points selector
        const dataPointsSelect = document.getElementById('dataPointsSelect');
        if (dataPointsSelect) {
            dataPointsSelect.addEventListener('change', (e) => {
                this.maxDataPoints = parseInt(e.target.value) || 0;
                this.updateCharts();
            });
        }
    }

    async loadData() {
        try {
            const params = new URLSearchParams();
            if (this.currentDevice) {
                params.append('device_id', this.currentDevice);
            }
            params.append('limit', this.maxDataPoints || 100);

            const response = await fetch(`${window.dashboardConfig.apiUrl}/sensor-data?${params}`);
            const result = await response.json();

            if (result.success) {
                this.sensorData = result.data;
                this.updateCharts();
                this.updateRecentDataTable();
                this.updateSummaryCards();
            }
        } catch (error) {
            console.error('Error loading data:', error);
            showNotification('Failed to load sensor data', 'danger');
        }
    }

    async loadChartData() {
        try {
            const params = new URLSearchParams();
            if (this.currentDevice) {
                params.append('device_id', this.currentDevice);
            }
            params.append('hours', 24);
            params.append('limit', this.maxDataPoints || 100);

            const response = await fetch(`/dashboard/chart-data?${params}`);
            const result = await response.json();

            if (result.success) {
                this.updateChartsWithData(result.chart_data);
            }
        } catch (error) {
            console.error('Error loading chart data:', error);
        }
    }

    updateChartsWithData(chartData) {
        // Update Temperature & Humidity Chart
        if (this.charts.tempHum) {
            this.charts.tempHum.data.labels = chartData.labels;
            this.charts.tempHum.data.datasets[0].data = chartData.datasets.temperature;
            this.charts.tempHum.data.datasets[1].data = chartData.datasets.humidity;
            this.charts.tempHum.update('none');
        }

        // Update Light Chart
        if (this.charts.light) {
            this.charts.light.data.labels = chartData.labels;
            this.charts.light.data.datasets[0].data = chartData.datasets.light;
            this.charts.light.update('none');
        }

        // Update Power Chart
        if (this.charts.power) {
            this.charts.power.data.labels = chartData.labels;
            this.charts.power.data.datasets[0].data = chartData.datasets.voltage;
            this.charts.power.data.datasets[1].data = chartData.datasets.current;
            this.charts.power.data.datasets[2].data = chartData.datasets.power;
            this.charts.power.update('none');
        }
    }

    updateCharts() {
        if (this.sensorData.length === 0) return;

        const labels = this.sensorData.map(data => {
            const date = new Date(data.timestamp);
            return date.toLocaleTimeString('en-US', { 
                hour12: false, 
                hour: '2-digit', 
                minute: '2-digit',
                second: '2-digit'
            });
        }).reverse();

        const tempData = this.sensorData.map(data => data.temperature).reverse();
        const humData = this.sensorData.map(data => data.humidity).reverse();
        const lightData = this.sensorData.map(data => data.light >= 0 ? data.light : null).reverse();
        const voltageData = this.sensorData.map(data => data.voltage).reverse();
        const currentData = this.sensorData.map(data => data.current).reverse();
        const powerData = this.sensorData.map(data => data.power).reverse();

        // Update charts
        this.updateChartsWithData({
            labels: labels,
            datasets: {
                temperature: tempData,
                humidity: humData,
                light: lightData,
                voltage: voltageData,
                current: currentData,
                power: powerData
            }
        });
    }

    updateRecentDataTable() {
        const tbody = document.getElementById('recentDataTable');
        if (!tbody) return;

        tbody.innerHTML = '';
        
        const recentData = this.sensorData.slice(0, 10);
        
        if (recentData.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center">No data available</td></tr>';
            return;
        }

        recentData.forEach(data => {
            const row = tbody.insertRow();
            const time = new Date(data.timestamp).toLocaleTimeString();
            
            row.innerHTML = `
                <td>${time}</td>
                <td><small>${data.device_id}</small></td>
                <td>${data.temperature !== null ? data.temperature.toFixed(1) + '°C' : 'N/A'}</td>
                <td>${data.light >= 0 ? data.light.toFixed(1) + ' lux' : 'N/A'}</td>
                <td>${data.power !== null ? data.power.toFixed(2) + ' mW' : 'N/A'}</td>
            `;
        });
    }

    updateSummaryCards() {
        if (this.sensorData.length === 0) return;

        const latestData = this.sensorData[0];
        
        if (latestData.temperature !== null) {
            document.getElementById('currentTemp').textContent = latestData.temperature.toFixed(1) + '°C';
        }
        
        if (latestData.power !== null) {
            document.getElementById('currentPower').textContent = latestData.power.toFixed(2) + ' mW';
        }
    }

    startDataUpdates() {
        // Update data every 30 seconds
        this.updateInterval = setInterval(() => {
            this.loadData();
        }, 30000);
    }

    stopDataUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    initializeCharts() {
        // Initialize charts will be called from charts.js
        if (window.chartManager) {
            this.charts = window.chartManager.charts;
        }
    }
}

// Control Functions
async function setInterval() {
    const deviceId = document.getElementById('deviceSelect').value;
    const interval = document.getElementById('intervalSelect').value;
    
    if (!deviceId) {
        showNotification('Please select a device first', 'warning');
        return;
    }

    const unit = parseInt(interval) >= 60 ? 'minutes' : 'seconds';
    const value = parseInt(interval) >= 60 ? parseInt(interval) / 60 : parseInt(interval);
    
    try {
        const response = await fetch(`${window.dashboardConfig.apiUrl}/devices/${deviceId}/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.dashboardConfig.csrfToken
            },
            body: JSON.stringify({
                action: 'set_interval',
                value: value,
                unit: unit
            })
        });

        const result = await response.json();
        
        if (result.success) {
            showNotification(`Interval set to ${value} ${unit}`, 'success');
        } else {
            showNotification(result.error || 'Failed to set interval', 'danger');
        }
    } catch (error) {
        console.error('Error setting interval:', error);
        showNotification('Failed to set interval', 'danger');
    }
}

async function forceReading() {
    const deviceId = document.getElementById('deviceSelect').value;
    
    if (!deviceId) {
        showNotification('Please select a device first', 'warning');
        return;
    }

    try {
        const response = await fetch(`${window.dashboardConfig.apiUrl}/devices/${deviceId}/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.dashboardConfig.csrfToken
            },
            body: JSON.stringify({
                action: 'force_reading'
            })
        });

        const result = await response.json();
        
        if (result.success) {
            showNotification('Force reading requested', 'success');
            // Reload data after a short delay
            setTimeout(() => {
                window.dashboard.loadData();
            }, 2000);
        } else {
            showNotification(result.error || 'Failed to force reading', 'danger');
        }
    } catch (error) {
        console.error('Error forcing reading:', error);
        showNotification('Failed to force reading', 'danger');
    }
}

async function getStatus() {
    const deviceId = document.getElementById('deviceSelect').value;
    
    if (!deviceId) {
        showNotification('Please select a device first', 'warning');
        return;
    }

    try {
        const response = await fetch(`/dashboard/device-status?device_id=${deviceId}`);
        const result = await response.json();
        
        if (result.success) {
            showDeviceStatus(result);
        } else {
            showNotification(result.error || 'Failed to get device status', 'danger');
        }
    } catch (error) {
        console.error('Error getting status:', error);
        showNotification('Failed to get device status', 'danger');
    }
}

function showDeviceStatus(statusData) {
    const modalBody = document.getElementById('statusModalBody');
    const device = statusData.device;
    const latestData = statusData.latest_data;
    
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Device Information</h6>
                <p><strong>Device ID:</strong> ${device.device_id}</p>
                <p><strong>Name:</strong> ${device.device_name || 'N/A'}</p>
                <p><strong>Location:</strong> ${device.location || 'N/A'}</p>
                <p><strong>Status:</strong> 
                    <span class="badge ${device.is_online ? 'bg-success' : 'bg-danger'}">
                        ${device.is_online ? 'Online' : 'Offline'}
                    </span>
                </p>
                <p><strong>Last Seen:</strong> ${device.last_seen ? new Date(device.last_seen).toLocaleString() : 'Never'}</p>
                <p><strong>Uptime:</strong> ${device.uptime_percentage}%</p>
            </div>
            <div class="col-md-6">
                <h6>Latest Sensor Data</h6>
                ${latestData ? `
                    <p><strong>Temperature:</strong> ${latestData.temperature !== null ? latestData.temperature + '°C' : 'N/A'}</p>
                    <p><strong>Humidity:</strong> ${latestData.humidity !== null ? latestData.humidity + '%' : 'N/A'}</p>
                    <p><strong>Light:</strong> ${latestData.light >= 0 ? latestData.light + ' lux' : 'N/A'}</p>
                    <p><strong>Voltage:</strong> ${latestData.voltage !== null ? latestData.voltage + 'V' : 'N/A'}</p>
                    <p><strong>Current:</strong> ${latestData.current !== null ? latestData.current + 'mA' : 'N/A'}</p>
                    <p><strong>Power:</strong> ${latestData.power !== null ? latestData.power + 'mW' : 'N/A'}</p>
                    <p><strong>Sensors:</strong>
                        <span class="badge ${latestData.sensor_status.dht11 ? 'bg-success' : 'bg-danger'}">DHT11</span>
                        <span class="badge ${latestData.sensor_status.bh1750 ? 'bg-success' : 'bg-danger'}">BH1750</span>
                        <span class="badge ${latestData.sensor_status.ina219 ? 'bg-success' : 'bg-danger'}">INA219</span>
                    </p>
                ` : '<p>No recent data available</p>'}
            </div>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

function switchDevice() {
    if (window.dashboard) {
        window.dashboard.loadData();
    }
}

function updateChartLimit() {
    if (window.dashboard) {
        const limit = document.getElementById('dataPointsSelect').value;
        window.dashboard.maxDataPoints = parseInt(limit) || 0;
        window.dashboard.loadData();
    }
}

// Initialize dashboard
function initializeDashboard() {
    window.dashboard = new SmartLampDashboard();
}

// Global functions for backward compatibility
window.setInterval = setInterval;
window.forceReading = forceReading;
window.getStatus = getStatus;
window.switchDevice = switchDevice;
window.updateChartLimit = updateChartLimit;

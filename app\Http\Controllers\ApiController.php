<?php

namespace App\Http\Controllers;

use App\Models\SensorData;
use App\Models\DeviceSettings;
use App\Models\SystemLogs;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ApiController extends Controller
{
    /**
     * Store sensor data from MQTT or direct API call
     */
    public function storeSensorData(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'timestamp' => 'required',
            'device_id' => 'required|string|max:50',
            'counter' => 'integer|min:0',
            'temperature' => 'nullable|numeric|between:-50,100',
            'humidity' => 'nullable|numeric|between:0,100',
            'light' => 'nullable|numeric|min:0',
            'voltage' => 'nullable|numeric|min:0',
            'current' => 'nullable|numeric',
            'power' => 'nullable|numeric',
            'interval' => 'integer|min:1000',
            'sensor_status' => 'array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        try {
            $data = $request->all();
            
            // Parse timestamp
            $timestamp = $data['timestamp'];
            if (is_numeric($timestamp)) {
                // Convert milliseconds to seconds if needed
                if ($timestamp > 9999999999) {
                    $timestamp = $timestamp / 1000;
                }
                $sensorTimestamp = Carbon::createFromTimestamp($timestamp);
            } else {
                $sensorTimestamp = Carbon::parse($timestamp);
            }

            // Extract sensor status
            $sensorStatus = $data['sensor_status'] ?? [];
            
            // Calculate power if not provided
            $power = $data['power'] ?? null;
            if ($power === null && isset($data['voltage']) && isset($data['current'])) {
                $power = $data['voltage'] * $data['current'];
            }

            // Create sensor data record
            $sensorData = SensorData::create([
                'sensor_timestamp' => $sensorTimestamp,
                'device_id' => $data['device_id'],
                'counter' => $data['counter'] ?? 0,
                'temperature' => $data['temperature'],
                'humidity' => $data['humidity'],
                'light' => $data['light'] ?? -1,
                'voltage' => $data['voltage'],
                'current' => $data['current'],
                'power' => $power,
                'dht11_status' => $sensorStatus['dht11'] ?? false,
                'bh1750_status' => $sensorStatus['bh1750'] ?? false,
                'ina219_status' => $sensorStatus['ina219'] ?? false,
                'interval_ms' => $data['interval'] ?? 30000,
                'raw_data' => $data
            ]);

            // Update or create device settings
            $device = DeviceSettings::findOrCreateDevice($data['device_id']);
            $device->updateLastSeen();
            $device->incrementDataPoints();
            
            // Update interval if provided
            if (isset($data['interval'])) {
                $device->update(['data_interval' => $data['interval']]);
            }

            // Log the event
            SystemLogs::create([
                'log_timestamp' => now(),
                'device_id' => $data['device_id'],
                'level' => 'INFO',
                'event_type' => 'data_received',
                'message' => 'Sensor data received and stored successfully',
                'context' => [
                    'record_id' => $sensorData->id,
                    'counter' => $data['counter'] ?? 0,
                    'working_sensors' => $sensorData->working_sensors_count
                ],
                'ip_address' => $request->ip()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Data stored successfully',
                'data' => [
                    'id' => $sensorData->id,
                    'timestamp' => $sensorData->sensor_timestamp->toISOString(),
                    'device_id' => $sensorData->device_id,
                    'working_sensors' => $sensorData->working_sensors_count
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Error storing sensor data', [
                'error' => $e->getMessage(),
                'data' => $request->all(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to store sensor data',
                'message' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get sensor data with filtering and pagination
     */
    public function getSensorData(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'device_id' => 'string|max:50',
            'start_date' => 'date',
            'end_date' => 'date|after_or_equal:start_date',
            'limit' => 'integer|min:1|max:1000',
            'page' => 'integer|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        try {
            $deviceId = $request->get('device_id');
            $startDate = $request->get('start_date') ? Carbon::parse($request->get('start_date')) : null;
            $endDate = $request->get('end_date') ? Carbon::parse($request->get('end_date')) : null;
            $limit = $request->get('limit', 100);
            $page = $request->get('page', 1);

            $query = SensorData::query()->orderBy('sensor_timestamp', 'desc');

            if ($deviceId) {
                $query->forDevice($deviceId);
            }

            if ($startDate && $endDate) {
                $query->inDateRange($startDate, $endDate);
            }

            $data = $query->paginate($limit, ['*'], 'page', $page);

            $formattedData = $data->getCollection()->map(function ($item) {
                return [
                    'id' => $item->id,
                    'timestamp' => $item->sensor_timestamp->toISOString(),
                    'device_id' => $item->device_id,
                    'counter' => $item->counter,
                    'temperature' => $item->temperature,
                    'humidity' => $item->humidity,
                    'light' => $item->light,
                    'voltage' => $item->voltage,
                    'current' => $item->current,
                    'power' => $item->power_calculated,
                    'sensor_status' => $item->sensor_status,
                    'interval_ms' => $item->interval_ms,
                    'working_sensors' => $item->working_sensors_count
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedData,
                'pagination' => [
                    'current_page' => $data->currentPage(),
                    'last_page' => $data->lastPage(),
                    'per_page' => $data->perPage(),
                    'total' => $data->total(),
                    'from' => $data->firstItem(),
                    'to' => $data->lastItem(),
                    'has_more' => $data->hasMorePages()
                ],
                'filters' => [
                    'device_id' => $deviceId,
                    'start_date' => $startDate?->toISOString(),
                    'end_date' => $endDate?->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving sensor data', [
                'error' => $e->getMessage(),
                'params' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve sensor data'
            ], 500);
        }
    }

    /**
     * Send command to device (for MQTT integration)
     */
    public function sendCommand(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'device_id' => 'required|string|max:50',
            'action' => 'required|string|in:set_interval,get_status,force_reading,get_interval',
            'value' => 'integer|min:1',
            'unit' => 'string|in:seconds,minutes,s,m'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        try {
            $deviceId = $request->get('device_id');
            $action = $request->get('action');
            $value = $request->get('value');
            $unit = $request->get('unit', 'seconds');

            // Find device
            $device = DeviceSettings::where('device_id', $deviceId)->first();
            if (!$device) {
                return response()->json([
                    'success' => false,
                    'error' => 'Device not found'
                ], 404);
            }

            $response = ['success' => true, 'action' => $action];

            switch ($action) {
                case 'set_interval':
                    if (!$value) {
                        return response()->json([
                            'success' => false,
                            'error' => 'Value is required for set_interval action'
                        ], 400);
                    }

                    // Convert to milliseconds
                    $intervalMs = match($unit) {
                        'minutes', 'm' => $value * 60 * 1000,
                        default => $value * 1000
                    };

                    $oldInterval = $device->data_interval;
                    $device->update(['data_interval' => $intervalMs]);

                    $response = array_merge($response, [
                        'old_interval' => $oldInterval,
                        'new_interval' => $intervalMs,
                        'seconds' => $intervalMs / 1000,
                        'minutes' => $intervalMs / 60000
                    ]);
                    break;

                case 'get_interval':
                    $response = array_merge($response, [
                        'interval_ms' => $device->data_interval,
                        'interval_seconds' => $device->data_interval_seconds,
                        'interval_minutes' => $device->data_interval_minutes
                    ]);
                    break;

                case 'get_status':
                    $latestData = $device->latestSensorData;
                    $response = array_merge($response, [
                        'device_status' => [
                            'device_id' => $device->device_id,
                            'is_online' => $device->is_online,
                            'last_seen' => $device->last_seen?->toISOString(),
                            'uptime_percentage' => $device->uptime_percentage,
                            'total_data_points' => $device->total_data_points
                        ],
                        'sensor_status' => $latestData?->sensor_status ?? [
                            'dht11' => false,
                            'bh1750' => false,
                            'ina219' => false
                        ],
                        'latest_reading' => $latestData ? [
                            'timestamp' => $latestData->sensor_timestamp->toISOString(),
                            'temperature' => $latestData->temperature,
                            'humidity' => $latestData->humidity,
                            'light' => $latestData->light,
                            'power' => $latestData->power_calculated
                        ] : null
                    ]);
                    break;

                case 'force_reading':
                    $response['message'] = 'Force reading command sent';
                    break;
            }

            // Log the command
            SystemLogs::create([
                'log_timestamp' => now(),
                'device_id' => $deviceId,
                'level' => 'INFO',
                'event_type' => 'command_sent',
                'message' => "Command '{$action}' sent to device",
                'context' => $request->all(),
                'ip_address' => $request->ip()
            ]);

            // Here you would typically publish the command to MQTT
            // For now, we'll just return the response
            
            return response()->json($response);

        } catch (\Exception $e) {
            Log::error('Error sending command', [
                'error' => $e->getMessage(),
                'command' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to send command'
            ], 500);
        }
    }

    /**
     * Get device list
     */
    public function getDevices(): JsonResponse
    {
        try {
            $devices = DeviceSettings::getDeviceList();
            
            return response()->json([
                'success' => true,
                'devices' => $devices,
                'summary' => [
                    'total' => count($devices),
                    'online' => collect($devices)->where('is_online', true)->count(),
                    'active' => collect($devices)->where('status', 'online')->count()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving devices', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve devices'
            ], 500);
        }
    }

    /**
     * Health check endpoint
     */
    public function health(): JsonResponse
    {
        try {
            // Check database connection
            $dbStatus = \DB::connection()->getPdo() ? 'connected' : 'disconnected';
            
            // Get system stats
            $stats = [
                'total_devices' => DeviceSettings::count(),
                'online_devices' => DeviceSettings::online()->count(),
                'total_records' => SensorData::count(),
                'recent_records' => SensorData::where('created_at', '>=', now()->subHour())->count()
            ];

            return response()->json([
                'success' => true,
                'status' => 'healthy',
                'timestamp' => now()->toISOString(),
                'database' => $dbStatus,
                'statistics' => $stats,
                'version' => config('app.version', '1.0.0')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

@extends('layouts.app')

@section('title', 'Smart Lamp Dashboard')

@section('content')
<div class="row">
    <!-- Summary Cards -->
    <div class="col-md-3 mb-4">
        <div class="status-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 id="totalDevices">{{ $totalDevices }}</h3>
                    <p class="mb-0">Total Devices</p>
                </div>
                <i class="fas fa-microchip fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="status-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 id="onlineDevices">{{ $onlineDevices }}</h3>
                    <p class="mb-0">Online Devices</p>
                </div>
                <i class="fas fa-wifi fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="status-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 id="currentTemp">--°C</h3>
                    <p class="mb-0">Temperature</p>
                </div>
                <i class="fas fa-thermometer-half fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="status-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 id="currentPower">-- mW</h3>
                    <p class="mb-0">Power</p>
                </div>
                <i class="fas fa-bolt fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Control Panel -->
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Control Panel</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="deviceSelect" class="form-label">Select Device:</label>
                        <select class="form-select" id="deviceSelect" onchange="switchDevice()">
                            <option value="">All Devices</option>
                            @foreach($devices as $device)
                                <option value="{{ $device['device_id'] }}">
                                    {{ $device['device_name'] ?? $device['device_id'] }}
                                    @if($device['is_online'])
                                        <span class="text-success">●</span>
                                    @else
                                        <span class="text-danger">●</span>
                                    @endif
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="intervalSelect" class="form-label">Data Interval:</label>
                        <select class="form-select" id="intervalSelect">
                            <option value="5">5 seconds</option>
                            <option value="10">10 seconds</option>
                            <option value="30" selected>30 seconds</option>
                            <option value="60">1 minute</option>
                            <option value="300">5 minutes</option>
                            <option value="600">10 minutes</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="dataPointsSelect" class="form-label">Chart Data Points:</label>
                        <select class="form-select" id="dataPointsSelect" onchange="updateChartLimit()">
                            <option value="50">Last 50 points</option>
                            <option value="100" selected>Last 100 points</option>
                            <option value="200">Last 200 points</option>
                            <option value="500">Last 500 points</option>
                            <option value="0">Unlimited</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Actions:</label><br>
                        <button class="btn btn-primary btn-sm me-2" onclick="setInterval()">
                            <i class="fas fa-clock"></i> Set Interval
                        </button>
                        <button class="btn btn-success btn-sm me-2" onclick="forceReading()">
                            <i class="fas fa-sync"></i> Force Reading
                        </button>
                        <button class="btn btn-info btn-sm" onclick="getStatus()">
                            <i class="fas fa-info"></i> Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Temperature & Humidity Chart -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Temperature & Humidity</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="tempHumChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Light Level Chart -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-sun me-2"></i>Light Level</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="lightChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Power Monitoring Chart -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Power Monitoring</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="powerChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Data -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Recent Data</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Device</th>
                                <th>Temp</th>
                                <th>Light</th>
                                <th>Power</th>
                            </tr>
                        </thead>
                        <tbody id="recentDataTable">
                            <tr>
                                <td colspan="5" class="text-center">
                                    <div class="loading-spinner"></div>
                                    Loading...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Export Section -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-download me-2"></i>Data Export</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="startDate" class="form-label">Start Date:</label>
                        <input type="datetime-local" class="form-control" id="startDate">
                    </div>
                    <div class="col-md-3">
                        <label for="endDate" class="form-label">End Date:</label>
                        <input type="datetime-local" class="form-control" id="endDate">
                    </div>
                    <div class="col-md-2">
                        <label for="exportFormat" class="form-label">Format:</label>
                        <select class="form-select" id="exportFormat">
                            <option value="csv">CSV</option>
                            <option value="json">JSON</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Quick Export:</label><br>
                        <button class="btn btn-outline-primary btn-sm me-1" onclick="exportQuick('today')">Today</button>
                        <button class="btn btn-outline-primary btn-sm me-1" onclick="exportQuick('week')">Week</button>
                        <button class="btn btn-outline-primary btn-sm me-1" onclick="exportQuick('month')">Month</button>
                        <button class="btn btn-success btn-sm" onclick="exportData()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Device Status Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Device Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="statusModalBody">
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    Loading device status...
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/dashboard.js') }}"></script>
<script src="{{ asset('js/mqtt-client.js') }}"></script>
<script src="{{ asset('js/charts.js') }}"></script>
<script src="{{ asset('js/export.js') }}"></script>

<script>
    // Initialize dashboard with Laravel data
    window.dashboardData = {
        devices: @json($devices),
        latestData: @json($latestData ?? [])
    };

    // Initialize dashboard when page loads
    document.addEventListener('DOMContentLoaded', function() {
        initializeDashboard();
        initializeDateInputs();
        loadInitialData();
    });

    function initializeDateInputs() {
        const now = new Date();
        const today = now.toISOString().slice(0, 16);
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16);
        
        document.getElementById('endDate').value = today;
        document.getElementById('startDate').value = weekAgo;
    }

    function loadInitialData() {
        // Update summary cards with latest data
        if (window.dashboardData.latestData) {
            updateSummaryCards(window.dashboardData.latestData);
        }
        
        // Load chart data
        loadChartData();
        
        // Load recent data table
        loadRecentData();
    }

    function updateSummaryCards(latestData) {
        // Find the most recent data across all devices
        let latestTemp = null;
        let latestPower = null;
        
        Object.values(latestData).forEach(data => {
            if (data.temperature !== null) latestTemp = data.temperature;
            if (data.power !== null) latestPower = data.power;
        });
        
        if (latestTemp !== null) {
            document.getElementById('currentTemp').textContent = latestTemp.toFixed(1) + '°C';
        }
        
        if (latestPower !== null) {
            document.getElementById('currentPower').textContent = latestPower.toFixed(2) + ' mW';
        }
    }
</script>
@endpush
